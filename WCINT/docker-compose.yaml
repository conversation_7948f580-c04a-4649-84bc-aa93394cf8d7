services:
#   # MySQL 数据库服务 [6,8](@ref)
#   mysql:
#     image: mysql:8.0
#     container_name: mysql-dev
#     environment:
#       MYSQL_ROOT_HOST: '%'
#       MYSQL_ROOT_PASSWORD: 123456
#       MYSQL_DATABASE: app_service
#       TZ: Asia/Shanghai
#     ports:
#       - "3306:3306"
#     volumes:
#       - ./data/mysql:/var/lib/mysql
#     networks:
#       - dev-network
#     healthcheck:
#       test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
#       interval: 10s
#       timeout: 5s
#       retries: 3
#     command:
#       - mysqld
#       - --character-set-server=utf8mb4
#       - --collation-server=utf8mb4_unicode_ci
#       - --default-authentication-plugin=mysql_native_password
#       - --bind-address=0.0.0.0
#
#   # Redis 缓存服务 [9,10](@ref)
#   redis:
#     image: redis:7.0-alpine
#     container_name: redis-dev
#     ports:
#       - "6379:6379"
#     volumes:
#       - ./data/redis:/data
#     networks:
#       - dev-network
#     command: redis-server --save 60 1 --loglevel warning

  # Zookeeper 服务（Kafka 依赖）[12,13](@ref)
  zookeeper:
    image: confluentinc/cp-zookeeper:7.3.0
    container_name: zookeeper-dev
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - dev-network

  # Kafka 消息队列服务 [12,14](@ref)
  kafka:
    image: confluentinc/cp-kafka:7.3.0
    container_name: kafka-dev
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    volumes:
      - ./data/kafka:/var/lib/kafka/data
    networks:
      - dev-network

#   # MongoDB 数据库服务
#   mongodb:
#     image: mongo:6.0
#     container_name: mongodb-dev
#     environment:
#       MONGO_INITDB_ROOT_USERNAME: admin
#       MONGO_INITDB_ROOT_PASSWORD: 123456
#       TZ: Asia/Shanghai
#     ports:
#       - "27017:27017"
#     volumes:
#       - ./data/mongodb:/data/db
#     networks:
#       - dev-network

# 自定义网络配置
networks:
  dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷声明
volumes:
#   mysql-data:
#   redis-data:
  kafka-data:
#   mongodb-data:
